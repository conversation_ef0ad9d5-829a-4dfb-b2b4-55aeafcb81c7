<template>
  <div class="research-analysis">
    <div class="result-tab">
      <div
        v-for="(tab, index) in roleAssistantTabList.filter((item: any) => item.show)"
        :class="`tab-item ${active === tab.questionType ? 'active' : ''}`"
        @click="onChangeTab(tab.questionType)"
      >
        <!-- <ysIcon class="icon" :type="tab.icon" /> -->
        {{ tab.label }}
      </div>
    </div>
    <div class="result-content" id="resultContent">
      <div v-if="active === 2" style="padding: 0 20px">
        <classStructure />
      </div>
      <div v-if="active === 27" style="padding: 20px">
        <ysEmpt v-if="!tableData27.length" />
        <questionScatter />
        <div style="display: flex;margin-top: 24px;">
          <Bloom style="width: 50%;" />
          <FourRader style="width: 50%;" />
        </div>
      </div>
      <div v-if="active === 30" style="padding: 20px">
        <div v-show="!empty30">
          <div class="nav30">
            <div class="nav30-left">
              <div class="implementation-item">
                <div class="block excellent">优</div>
                <span class="num">{{ implementationNums.excellent }}</span>
              </div>
              <div class="implementation-item">
                <div class="block good">良</div>
                <span class="num">{{ implementationNums.good }}</span>
              </div>
              <div class="implementation-item">
                <div class="block poor">差</div>
                <span class="num">{{ implementationNums.poor }}</span>
              </div>
              <div class="implementation-item">
                <div class="block not">未</div>
                <span class="num">{{ implementationNums.not }}</span>
              </div>
            </div>
            <div class="nav30-right">
              <a-select
                ref="select"
                v-model:value="value30"
                style="width: 120px"
                @change="onChangeImplementation"
              >
                <a-select-option value="全部">全部</a-select-option>
                <a-select-option value="优">优</a-select-option>
                <a-select-option value="良">良</a-select-option>
                <a-select-option value="差">差</a-select-option>
                <a-select-option value="未">未</a-select-option>
              </a-select>
            </div>
          </div>
          <a-table
            :dataSource="tableData30"
            :columns="columns30"
            :pagination="false"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'startTime'">
                <span
                  style="color: #007aff; cursor: pointer"
                  @click="onJumpVideoCurrent(record.startTime)"
                  >{{ record.startTime }}</span
                >
              </template>
              <template v-if="column.dataIndex === '教学设计内容'">
                <div class="record30">
                  <div class="record30-item">
                    <div class="title1">教学阶段</div>
                    <div class="text">{{ record["教学阶段"] }}</div>
                  </div>
                  <div class="record30-item">
                    <div class="title1">教学环节</div>
                    <div class="text">{{ record["教学环节"] }}</div>
                  </div>
                  <div class="record30-item">
                    <div class="title1">教学方式</div>
                    <div class="text">{{ record["教学方式"] }}</div>
                  </div>
                  <div class="record30-item">
                    <div class="title1">教学内容</div>
                    <div class="text">{{ record["教学内容"] }}</div>
                  </div>
                  <div class="record30-item">
                    <div class="title1">教师活动</div>
                    <div class="text">{{ record["教师活动"] }}</div>
                  </div>
                  <div class="record30-item">
                    <div class="title1">学生活动</div>
                    <div class="text">{{ record["学生活动"] }}</div>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === '执行情况'">
                <div>
                  {{ record["执行程度分析"] }}
                </div>
              </template>
              <template v-if="column.dataIndex === '执行度'">
                <div
                  class="implementation-level"
                  :class="bindClass29Column(record)"
                >
                  {{ record["执行程度"] }}
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div
          style="
            width: 100%;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          "
          v-show="empty30"
        >
          <img :src="pic" width="200" height="124" />
          <p style="color: #8c8c8c; margin-top: 8px">
            该课程未上传教案，无法进行执行度分析
          </p>
        </div>
      </div>
      <div class="tips23" v-show="active === 23">
        <div class="tips23-box">
          <ysIcon type="iconjinggao" />
          <span class="text">该内容为AI根据课程的内容反推老师的教学设计。</span>
        </div>
      </div>
      <div
        v-show="active !== 2 && active !== 18 && active !== 27 && active !== 30 && active !== 36 && active !== 37"
        style="padding: 24px"
        class="AICONTENT"
        id="AICONTENT"
        v-html="html"
      ></div>

      <div class="ai-fansi" v-show="active === 36">
        <div class="ai-fansi-box">
          <div class="fansi-item" v-for="(item, index) in tableData36" :key="index">
            <div class="fansi-icon-box">
              <div class="fansi-icon" :style="{ backgroundColor: item.color }">
                <img :src="item.icon" alt="" class="icon-img">
              </div>
              <span>{{ item.title }}</span>
            </div>
            <div class="fansi-content">{{ item.text }}</div>
          </div>
        </div>
      </div>

      <div class="ai-pingfen" v-show="active === 37">
        <p class="ai-pingfen-title">
          <h3>总分：{{ totalScoreOfTree }}</h3>
          <div style="color: #007AFF;cursor: pointer;display: flex;align-items: center;" @click="exportAiScore">
            <ysIcon type="icondaochu1" class="icon1" style="font-size: 16px;" />
            <span style="margin-left: 4px;">导出</span>
          </div>
        </p>

        <div class="ai-pingfen-tree">
          <a-tree
            v-if="treeData37.length"
            showLine
            defaultExpandAll
            :fieldNames="{ children: 'children', label: 'name', value: 'id' }"
            :tree-data="treeData37"
            class="ai-tree"
          >
            <template #title="{ dataRef }">
              <div class="title-container">
                <div class="title-top" :style="{ marginBottom: dataRef.itemDescribe ? '0' : '8px', paddingLeft: dataRef.children?.length ? '0' : '12px'}">
                  <div style="line-height: 1.5;">{{ dataRef.name }}</div>
                  <a-popover v-if="dataRef.type === 1" placement="bottomRight" :overlayStyle="{ width: '400px' }">
                    <template #content>
                      <p>{{ dataRef.reason }}</p>
                    </template>
                    <template #title>
                      <span style="font-weight: bold;">得分原因</span>
                    </template>
                    <span class="score">得分：{{ dataRef.finalScore + '/' + dataRef.score }}</span>
                  </a-popover>
                </div>
                <div v-if="dataRef.itemDescribe" class="desc" :style="{paddingLeft: dataRef.children?.length ? '0' : '12px'}">描述：{{ dataRef.itemDescribe }}</div>
              </div>
            </template>
          </a-tree>
        </div>
      </div>

      <div class="content-summary" v-show="active === 18">
        <div class="content">
          <div class="content-left">
            <span class="icon-box">
              <img src="@/assets/images/content_summary_1.png" alt="" />
            </span>
            <span>课堂内容总结</span>
          </div>
          <div class="content-right">
            <p class="text ellipsis">{{ data18?.['内容总结'] || '' }}</p>
          </div>
        </div>
        <div class="content">
          <div class="content-left">
            <span class="icon-box">
              <img src="@/assets/images/content_summary_2.png" alt="" />
            </span>
            <span>主要知识点</span>
          </div>
          <div class="content-right">
            <p class="text ellipsis">
              <template v-for="(item, index) in data18?.['知识点']" :key="index">{{ index + 1 }}.{{ item }}<br /></template>
            </p>
          </div>
        </div>
        <div class="content">
          <div class="content-left">
            <span class="icon-box">
              <img src="@/assets/images/content_summary_3.png" alt="" />
            </span>
            <span>值得称赞的地方</span>
          </div>
          <div class="content-right">
            <p class="text ellipsis">
              <template v-for="(item, index) in data18?.['赞点']" :key="index">{{ index + 1 }}.{{ item }}<br /></template>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="ai-jiaoan" v-show="active === 23">
      <!-- <a-tooltip placement="left">
        <template #title>
          <span>优化建议</span>
        </template>
        <div class="ai-jiaoan-item" @click="onJumpYouhua">
          <div class="num">3</div>
          <ysIcon type="iconkaoshi1" class="icon1" />
        </div>
      </a-tooltip> -->

      <a-tooltip placement="left">
        <template #title>
          <span>导出</span>
        </template>
        <div class="ai-jiaoan-item last" @click="onExportYouhua">
          <ysIcon type="icondaochu1" class="icon1" />
        </div>
      </a-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import pic from "./pic.png";
import { ysIcon, ysEmpt } from "@ys/ui";
import { marked } from "marked";
import classStructure from "./../charTabContainer/claStructure/index.vue";
import { ref, onMounted, inject, Ref, nextTick, computed } from "vue";
import { exportAiTeachPlan } from "@/pages/videoAudioBrainOld/api";
import { exportAiForm, selectEventInfoPlug } from "@/api/aiSettingController";
import { useRoute } from "vue-router";
import { emitter } from "@/utils/mitt";
import { identity } from "@vueuse/core";
import questionScatter from "./../charTabContainer/questionScatter/index.vue";
import Bloom from "../charTabContainer/BloomNew/index.vue"
import FourRader from "../charTabContainer/FourRader/index.vue"
import rethink1 from "@/assets/images/rethink_1.png";
import rethink2 from "@/assets/images/rethink_2.png";
import rethink3 from "@/assets/images/rethink_3.png";
import rethink4 from "@/assets/images/rethink_4.png";
import { AiTaskGroupVo } from "./../../entity";
import { common } from "@ys/tools"
import { getSessionUser } from "@ys/tools/common/user";

const classroomType = inject<Ref<number>>("classroomType");
const aiTaskGroupVo = inject<Ref<AiTaskGroupVo>>("aiTaskGroupVo");

const route = useRoute();

const data18 = ref(null);

const tableData27 = ref([]);

const columns30 = ref([
  {
    title: "时间",
    dataIndex: "startTime",
    key: "startTime",
    width: 88,
  },
  {
    title: "教学设计内容",
    dataIndex: "教学设计内容",
    key: "教学设计内容",
    width: 360,
  },
  {
    title: "执行情况",
    dataIndex: "执行情况",
    key: "执行情况",
    width: 150,
  },
  {
    title: "执行度",
    dataIndex: "执行度",
    key: "执行度",
  },
]);

const empty30 = ref(false);
const tableData30 = ref([]);
const tableDataClone30 = ref([]);
const implementationNums = ref({
  excellent: 0,
  good: 0,
  poor: 0,
  not: 0,
});
const value30 = ref("全部");

const onChangeImplementation = () => {
  if (value30.value === "全部") {
    tableData30.value = tableDataClone30.value;
  } else {
    tableData30.value = tableDataClone30.value.filter((item: any) => {
      return item["执行程度"] === value30.value;
    });
  }
};
const bindClass27Column = (record: any) => {
  const levelClassMap: { [key: string]: string } = {
    创造: "chuangzao",
    评价: "pingjia",
    应用: "yingyong",
    理解: "lijie",
    记忆: "jiyi",
    分析: "fenxi",
  };
  return levelClassMap[record["布鲁姆"]] || "hidden";
};

const bindClass29Column = (record: any) => {
  const levelClassMap: { [key: string]: string } = {
    优: "excellent",
    良: "good",
    差: "poor",
    未: "not",
  };
  return levelClassMap[record["执行程度"]] || "";
};

const tableData36 = ref<{text: string, icon: string, title: string, color: string}[]>([
  {
    text: "",
    icon: rethink1,
    title: "教学效果",
    color: "#27A3FA",
  },
  {
    text: "",
    icon: rethink2,
    title: "教学收获",
    color: "#13C2C2",
  },
  {
    text: "",
    icon: rethink3,
    title: "不足之处",
    color: "#FAAD14",
  },
  {
    text: "",
    icon: rethink4,
    title: "改进措施",
    color: "#7262FD",
  },
]);

const treeData37 = ref<any[]>([]);
const exportAiScore = async () => {
  try {
    const response = await exportAiForm({
      groupId: route.query.groupId,
    });

    let a = document.createElement("a");
    let link = URL.createObjectURL(response.data as Blob);
    a.setAttribute("href", link);
    a.download = `${aiTaskGroupVo?.value.groupName}-AI评分表.docx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(link);
  } catch (error) {
    console.error('导出失败', error);
  }
}

const calculateTotalScore = (node: any, isFinal: boolean = false): number => {
  if (!node || !node.children || node.children.length === 0) {
    return 0;
  }

  return node.children.reduce((sum: number, child: any) => {
    let currentScore = 0;
    if (isFinal) {
      if (child.type === 1 && typeof child.finalScore === 'number') {
        currentScore = child.finalScore;
      }
    } else {
      if (child.type === 1 && typeof child.score === 'number') {
        currentScore = child.score;
      }
    }

    return sum + currentScore + calculateTotalScore(child, isFinal);
  }, 0);
};

const totalScoreOfTree = computed(() => {
  let total = 0;
  treeData37.value.forEach(rootNode => {
    if (rootNode.type === 1 && typeof rootNode.finalScore === 'number') {
      total += rootNode.finalScore;
    }
    total += calculateTotalScore(rootNode, true);
  });
  return total;
});

let roleAssistantTabList = ref([
  {
    label: "内容总结",
    questionType: 18,
    // icon: "iconwenjian2",
    show: true,
  },
  {
    label: "课堂结构",
    questionType: 2,
    // icon: "icon-apartment",
    show: true,
  },
  {
    label: "问题分析",
    questionType: 27,
    // icon: "iconlingyong",
    show: true,
  },
  {
    label: "执行度分析",
    questionType: 30,
    // icon: "iconleidatu",
    show: true,
  },
  {
    label: "教学设计分析",
    questionType: 23,
    // icon: "iconfuwu",
    show: true,
  },
  {
    label: "教学反思",
    questionType: 36,
    show: true,
  },
  {
    label: "AI评分",
    questionType: 37,
    show: true,
  }
]);

// 判断tab权限
const judgeAuth = async () => {
  const eventId = route.params.id;
  const type = route.query.taskType;
  if (!eventId || eventId === '0') {
    roleAssistantTabList.value.find((item: any) => item.questionType === 36)!.show = false;
    return;
  }
  const response = await selectEventInfoPlug({ eventId, type });
  const res = response.data as any;
  if (res.code == 0) {
    if (!res.data?.setting && (aiTaskGroupVo?.value.lecturer !== getSessionUser()?.id)) {
      roleAssistantTabList.value.find((item: any) => item.questionType === 36)!.show = false;
    }
  }
  const list = roleChats?.value?.[3];
  if (!list || list.every((item: any) => item.questionType !== 37) || !listFormItem?.value || listFormItem.value.length === 0) {
    roleAssistantTabList.value.find((item: any) => item.questionType === 37)!.show = false;
  }
}
const roleChats = inject<Ref<any>>("roleChats");
const listFormItem = inject<Ref<any>>("listFormItem");
const active = ref(18);
const html = ref("");
const onChangeTab = (questionType: number) => {
  active.value = questionType;
  emitter.emit("on-change-current4-active", active.value);
  if (questionType == 27) {
    const list = roleChats?.value?.[3];
    if (!list) return;
    const find27 = list.find((item: any) => item.questionType == 27);
    if (find27) {
      const arr = JSON.parse(find27.answer);
      tableData27.value = arr;
    }
  } else if (questionType == 30) {
    value30.value = "全部";
    const list = roleChats?.value?.[3];
    if (!list) return;
    const find = list.find((item: any) => item.questionType == 30);
    if (find) {
      let arr = JSON.parse(find.answer);
      arr = arr.map((item: any) => {
        // let [s1, s2] = item["时间"].split("-")[0].split(":");
        item.startTime = item["时间"].split("-")[0];
        item.endTime = item["时间"].split("-")[1];
        return item;
      });
      console.log("arr", arr);
      tableData30.value = arr;
      tableDataClone30.value = arr;
      const implementationNumsMap = tableData30.value.reduce(
        (acc: any, item) => {
          const level = item["执行程度"];
          if (
            level === "优" ||
            level === "良" ||
            level === "差" ||
            level === "未"
          ) {
            acc[level] = (acc[level] || 0) + 1;
          }
          return acc;
        },
        {}
      );
      implementationNums.value.excellent = implementationNumsMap["优"] || 0;
      implementationNums.value.good = implementationNumsMap["良"] || 0;
      implementationNums.value.poor = implementationNumsMap["差"] || 0;
      implementationNums.value.not = implementationNumsMap["未"] || 0;
      console.log("implementationNums", implementationNums.value);
    } else {
      empty30.value = true;
    }
  } else if (questionType == 36) {
    const list = roleChats?.value?.[3];
    if (!list) return;
    const find36 = list.find((item: any) => item.questionType == 36);
    if (find36) {
      const obj = JSON.parse(find36.answer);
      tableData36.value[0].text = obj["教学效果"];
      tableData36.value[1].text = obj["教学收获"];
      tableData36.value[2].text = obj["不足之处"];
      tableData36.value[3].text = obj["改进措施"];
    } else {
      tableData36.value = [];
    }
  } else if (questionType == 37) {
    if (listFormItem && listFormItem.value.length > 0) {
      treeData37.value = listFormItem.value;
    } else {
      treeData37.value = [];
    }
  } else {
    initOverview();
  }
};

const initOverview = async () => {
  const list = roleChats?.value?.[3];
  if (!list) return;

  const item = list.find((item: any) => item.questionType === active.value);
  if (!item) return;
  if (active.value === 18 && item.answer) {
    const answer = JSON.parse(item.answer);

    if (typeof answer !== 'string') {
      data18.value = answer;
    }
  }

  if (active.value === 23) {
    const html1 = await marked.parse(item!.answer, {
      breaks: true,
    });
    // 优化建议
    const item2 = list.find((item: any) => item.questionType === 24);
    let html2 = await marked.parse(item2.answer, { breaks: true });
    const temptFooter = `<div class="tempt2" id="tempt2">
      <div class="bigIcon">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#iconkaoshi1"></use>
          </svg>
        </div>
        <div class="tempt-f">优化建议</div>
      </div>`;
    html.value =
      html1 + "<div style='margin-top: 40px'></div>" + temptFooter + html2;
    return;
  } else {
    // const html1 = await marked.parse(item!.answer, {
    //   breaks: true,
    // });
    // html.value = html1;
  }
};

const onJumpYouhua = () => {
  const tempt2 = document.getElementById("tempt2");
  const resultContent = document.getElementById("resultContent");
  const top = tempt2?.getBoundingClientRect().top;
  if (top && resultContent) {
    nextTick(() => {
      resultContent?.scrollTo(0, top);
    });
  }
};

const onExportYouhua = async () => {
  try {
    const response = await exportAiTeachPlan({ groupId: route.query.groupId });
    let a = document.createElement("a");
    let link = URL.createObjectURL(response.data as any);
    a.setAttribute("href", link);
    a.download = "优化建议.docx";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(link);
  } catch (error) {}
};

const onJumpVideoCurrent = (time: any) => {
  const [hours, minutes, seconds] = time.split(":").map(Number);
  const s = hours * 3600 + minutes * 60 + seconds;
  const aiVideo = document.getElementById("ai-video") as HTMLVideoElement;
  const aiVideo1 = document.getElementById("ai-video1") as HTMLVideoElement;
  if (aiVideo) {
    aiVideo.currentTime = s;
  }
  if (aiVideo1) {
    aiVideo1.currentTime = s;
  }
};

onMounted(() => {
  if (classroomType?.value == 2) {
    const clone = JSON.parse(JSON.stringify(roleAssistantTabList));
    roleAssistantTabList = clone.filter(
      (item: any) => item.questionType !== 27 && item.questionType !== 30
    );
  }

  judgeAuth();
  initOverview();
});
</script>

<style lang="scss" scoped>
$border: #e7e7e7;
$primary: #2469f1;
$txt3: #aab3c9;
$bg: #F7F7F7;
$txt1: #161e5d;

.research-analysis {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .result-tab {
    display: flex;
    align-items: center;

    .tab-item + .tab-item {
      margin-left: 4px;
    }

    .tab-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      flex: 1;
      cursor: pointer;
      font-weight: bold;
      color: #262626;
      position: relative;
      top: 1px;
      background: #fafafa;
      padding: 9px 8px;
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      .icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .tab-item.active {
      background: #fff;
      color: #007aff;
      border-bottom-color: #fff;
    }
  }
  .result-content {
    overflow-y: auto;
    flex: 1;
    border: 1px solid #e5e5e5;
  }
  .markmap-content {
    width: 100%;
    height: 575px;
  }
}

.ai-pingfen {
  padding: 20px;
  .ai-pingfen-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0;
    h3 {
      margin-bottom: 0;
    }
  }
  .ai-pingfen-tree {
    width: 100%;
    // padding-right: 48px;

    :deep(.ai-tree) {
      background: initial;
      .ant-tree-treenode {
        width: 100%;
        padding: 0 0 20px 0;
        position: static !important;

        .ant-tree-indent-unit {
          height: 140%;
        }

        .ant-tree-node-content-wrapper {
          // flex: 1;
          width: 100%;
          background: $bg;
          padding-left: 0;
          border-radius: 0px 5px 5px 0px;
          height: auto;
          border: 1px solid transparent;
        }

        .title-container {
          width: 100%;
          padding-right: 12px;
          overflow: hidden;

          .desc {
            color: #8C8C8C;
            line-height: 1.5;
            margin-bottom: 8px;
          }
        }

        .ant-tree-switcher {
          display: flex;
          align-items: center;
          padding-left: 12px;
          background: $bg;
          width: 40px;
          border-radius: 4px 0px 0px 4px;
          color: $txt3;
        }

        .ant-tree-indent-unit:before {
          border: 1px dashed $border;
        }

        .ant-tree-switcher {
          display: flex;
          align-items: center;
          padding-left: 12px;
          background: $bg;
          width: 40px;
          border-radius: 4px 0px 0px 4px;
          color: $txt3;
        }

        .ant-tree-node-content-wrapper {
          width: 100%;
          background: $bg;

          padding: 0;
          border-radius: 0px 4px 4px 0px;
          height: 100%;
        }

        .ant-tree-switcher-noop {
          display: none;
          &+.ant-tree-node-content-wrapper {
            border-radius: 4px;
          }
          &+.ant-tree-node-selected {
            border: 1px solid $primary;
          }
        }

        .ant-tree-node-selected .ant-tree-switcher {
          border: 1px solid $primary;
        }

        .text {
          height: 100%;
        }

        &:hover {
          .ant-tree-switcher-noop {
            &+.ant-tree-node-content-wrapper{
              background: #e8f3ff;
              border-right: 1px solid $primary;
              border-top: 1px solid $primary;
              border-bottom: 1px solid $primary;
              border-left: 1px solid $primary;
            }
            display: none;
          }
          .title-container {
            visibility: visible;
            color: $primary;
          }
          .ant-tree-switcher {
            background: #e8f3ff;
            border-left: 1px solid $primary;
            border-top: 1px solid $primary;
            border-bottom: 1px solid $primary;
          }
          .ant-tree-node-content-wrapper {
            background: #e8f3ff;
            border-right: 1px solid $primary;
            border-top: 1px solid $primary;
            border-bottom: 1px solid $primary;
          }
        }
      }

      .ant-tree-treenode-motion {
        width: 100%;
      }

      .ant-tree-treenode-selected {
        .ant-tree-switcher {
          border-radius: 4px 0 0 4px;
          border: 1px solid $primary;
          border-right: none;
          background: #e8f3ff;

          svg {
            fill: $primary;
          }
        }

        .ant-tree-node-content-wrapper {
          background: #e8f3ff;
          border-radius: 0 4px 4px 0;
          border: 1px solid $primary;
          border-left: none;
          color: $primary;

          .title {
            color: $primary !important;
          }
        }
      }
    }
  }
}

.title-top {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  .score {
    flex-shrink: 0;
    color: #007AFF;
    margin-left: 8px;
  }
}
</style>

<style lang="scss">
.research-analysis {
  position: relative;
  .tempt2 {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 36px;
    background: #e6f2ff;
    border-radius: 4px 4px 4px 4px;
    .bigIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background: #007aff;
      svg {
        width: 20px;
        height: 20px;
        fill: #fff;
      }
    }
    .tempt-f {
      margin-left: 12px;
      color: #262626;
      font-weight: bold;
    }
  }
  .tips23 {
    width: 100%;
    padding: 20px 20px 0 20px;
    .tips23-box {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      padding: 0 16px;
      box-sizing: border-box;
      background: #fffbe6;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #ffe07a;
      .text {
        margin-left: 10px;
      }
    }
  }
  .ai-jiaoan {
    position: absolute;
    right: 24px;
    top: 136px;
    z-index: 999;
    .ai-jiaoan-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: #f5f5f5;
      box-shadow: 0px 4px 13px 0px rgba(51, 51, 51, 0.16);
      border-radius: 900px 900px 900px 900px;
      cursor: pointer;
      .num {
        position: absolute;
        right: -10px;
        top: -5px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 18px;
        background: #f53f3f;
        color: #fff;
        border-radius: 100px 100px 100px 100px;
        border: 1px solid #ffffff;
      }
      &.last {
        margin-top: 12px;
      }
      .icon1 {
        font-size: 18px;
      }
    }
  }
  .ai-fansi {
    padding: 20px;
    .ai-fansi-box {
      display: flex;
      flex-direction: column;
      background-color: #fff;
      .fansi-item {
        display: flex;
        border: 1px solid #e5e5e5;
        &:not(:last-child) {
          border-bottom: none;
        }
        .fansi-icon-box {
          width: 120px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-color: #fff;
          border-right: 1px solid #e5e5e5;
          span {
            margin-top: 8px;
          }
        }
        .fansi-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          width: 36px;
          height: 36px;
          border-radius: 4px;
          .icon-img {
            width: 20px;
            height: 20px;
          }
        }
        .fansi-content {
          flex: 1;
        }
        .fansi-content {
          padding: 50px 20px;
        }
      }
    }
  }
}

.assessment-level {
  width: 48px;
  height: 24px;
  color: #fff;
  text-align: center;
  margin: 0 auto;
  line-height: 24px;
  border-radius: 4px;
  &.chuangzao {
    background: #f1584e;
  }
  &.pingjia {
    background: #f48a2e;
  }
  &.yingyong {
    background: #59be7f;
  }
  &.lijie {
    background: #4ab4d0;
  }
  &.jiyi {
    background: #8f7bd3;
  }
  &.fenxi {
    background: #facd0b;
  }
  &.hidden {
    display: none;
  }
}

.record30 {
  .record30-item {
    margin-bottom: 16px;
    display: flex;
    /* align-items: center; */
    &:last-child {
      margin-bottom: 0;
    }
    .title1 {
      flex: 0 0 auto;
      width: 68px;
      height: 24px;
      background: #e6f6ff;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #7ac8ff;
      font-size: 13px;
      text-align: center;
      line-height: 22px;
      color: #007aff;
    }
    .text {
      margin-left: 8px;
      color: #262626;
    }
  }
}
.nav30 {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .nav30-left {
    display: flex;
    align-items: center;
    .implementation-item {
      margin-right: 32px;
      display: flex;
      align-items: center;
      .block {
        width: 24px;
        height: 24px;
        text-align: center;
        line-height: 24px;
        border-radius: 4px 4px 4px 4px;
        color: #fff;
        &.excellent {
          background: #17be6b;
        }
        &.good {
          background: #007aff;
        }
        &.poor {
          background: #ffaa00;
        }
        &.not {
          background: #65789b;
        }
      }
      .num {
        margin-left: 6px;
      }
    }
  }
}
.implementation-level {
  width: 32px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px 4px 4px 4px;
  color: #fff;
  &.excellent {
    background: #17be6b;
  }
  &.good {
    background: #007aff;
  }
  &.poor {
    background: #ffaa00;
  }
  &.not {
    background: #65789b;
  }
}

.menu2-table {
  margin-bottom: 24px;
  position: relative;
  width: 100%;
  height: 40px;
  padding: 4px;
  box-sizing: border-box;
  border-radius: 6px;
  background: #f5f5f5;
  .slider {
    position: absolute;
    top: 4px;
    height: calc(100% - 8px);
    background: #fff;
    z-index: 2;
    transition: left 0.3s ease;
    border-radius: 6px;
  }
  .menu2-table-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 3;
    .menu2-table-item {
      flex: 1;
      height: 100%;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      transition: color 0.3s ease;
      &.active {
        color: #007aff;
      }
    }
  }
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.content-summary {
  margin: 20px;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  .content {
    display: flex;
    height: 274px;
    &:first-child {
      .icon-box {
        background: #27A3FA;
      }
    }
    &:nth-child(2) {
      .icon-box {
        background: #13C2C2;
      }
    }
    &:last-child {
      .icon-box {
        background: #FAAD14;
      }
    }
    &:not(:last-child) {
      border-bottom: 1px solid #e5e5e5;
    }
    .content-left {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 160px;
      border-right: 1px solid #e5e5e5;
      .icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 4px;
        margin-bottom: 8px;
        img {
          width: 20px;
          height: 20px;
        }
      }
    }
    .content-right {
      flex: 1;
      padding: 18px 20px;
      line-height: 22px;
      display: flex;
      align-items: center;
      .text {
        margin-bottom: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 11;
        line-clamp: 11;
      }
    }
  }
}
</style>

<style lang="scss">
.bloom-table {
  .ant-table-cell-row-hover {
    background: #e6f6ff !important;
  }
}
</style>
