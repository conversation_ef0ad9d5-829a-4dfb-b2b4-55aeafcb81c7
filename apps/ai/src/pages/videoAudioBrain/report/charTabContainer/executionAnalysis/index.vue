<template>
  <div class="execution-container">
    <ysEmpt v-if="Object.keys(executionData).length === 0" />
    <div v-else class="execution" id="execution"></div>
  </div>
</template>

<script setup lang="ts">
import { watch, inject, Ref, computed, nextTick } from "vue";
import * as echarts from "echarts";
import { ysEmpt } from "@ys/ui";

const roleChats = inject<Ref<any>>("roleChats");
const executionData = computed(() => {
  const list = roleChats?.value[3];
  const find30 = list.find((item: any) => item.questionType == 30);
  if (find30) {
    const arr = JSON.parse(find30.answer);

    const result = arr.reduce((acc: any, item: any) => {
      const degree = item["执行程度"];
      if (degree) {
        acc[degree]++;
      }
      return acc;
    }, {
      "优": 0,
      "良": 0,
      "差": 0,
      "未": 0
    });
    return result;
  }
  return {};
});

const initEcharts = () => {
  const colorMap: { [key: string]: string } = {
    "优": "#007AFF",
    "良": "#17BE6B",
    "差": "#FFAA00",
    "未": "#65789B"
  }

  const seriesData = Object.entries(executionData.value).map(([title, num]) => ({
    value: num,
    name: `${title} ${num}`,
    itemStyle: {
      color: colorMap[title]
    }
  }));

  const chartDom = document.getElementById("execution")!;
  const myChart = echarts.init(chartDom);
  let option: any;

  option = {
    title: {
      text: '执行度分布',
      textStyle: {
        fontSize: 14,
        color: "#262626",
        fontWeight: 'bold'
      },
    },
    legend: {
      top: 'center',
      right: '15%',
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: 14
      },
      orient: 'vertical',
      formatter: (name: string) => {
        return name.split(' ')[0];
      }
    },
    series: [
      {
        name: '执行程度',
        type: 'pie',
        radius: '50%',
        label: {
          show: true,
          fontSize: 14,
          color: "#262626",
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 15,
          smooth: true
        },
        data: seriesData.map(item => {
          if (item.value === 0) {
            return {
              ...item,
              label: { show: false },
              labelLine: { show: false }
            };
          }
          return item;
        })
      }
    ]
  };

  option && myChart.setOption(option);
};

watch(
  () => roleChats?.value,
  () => {
    nextTick(() => {
      initEcharts();
    });
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.execution-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.execution {
  width: 100%;
  height: 400px;
}
</style>